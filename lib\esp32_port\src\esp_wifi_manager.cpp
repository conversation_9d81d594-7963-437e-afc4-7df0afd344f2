#include "esp_wifi_manager.h"
#include "esp_log.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_netif.h"
#include "nvs_flash.h"
#include "lwip/err.h"
#include "lwip/sys.h"
#include "tuya_log.h"
#include <string.h>
#include <string>

static const char* TAG = "WiFiManager";

// 全局WiFi管理器实例
static esp_wifi_manager_t g_wifi_manager = {0};

// WiFi事件处理函数
static void wifi_event_handler(void* arg, esp_event_base_t event_base,
                              int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        TY_LOGI("WiFi station started");
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        wifi_event_sta_disconnected_t* event = (wifi_event_sta_disconnected_t*) event_data;
        TY_LOGI("WiFi disconnected, reason: %d", event->reason);
        
        g_wifi_manager.current_status = ESP_WIFI_STATUS_DISCONNECTED;
        g_wifi_manager.rssi = -100;
        memset(&g_wifi_manager.ip_addr, 0, sizeof(esp_ip4_addr_t));
        
        xEventGroupSetBits(g_wifi_manager.wifi_event_group, WIFI_DISCONNECTED_BIT);
        xEventGroupClearBits(g_wifi_manager.wifi_event_group, WIFI_CONNECTED_BIT);
        
        if (g_wifi_manager.event_callback) {
            g_wifi_manager.event_callback(ESP_WIFI_STATUS_DISCONNECTED);
        }
        
        // 自动重连
        esp_wifi_connect();
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        TY_LOGI("Got IP: " IPSTR, IP2STR(&event->ip_info.ip));
        
        g_wifi_manager.current_status = ESP_WIFI_STATUS_CONNECTED;
        g_wifi_manager.ip_addr = event->ip_info.ip;
        
        // 获取RSSI
        wifi_ap_record_t ap_info;
        if (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK) {
            g_wifi_manager.rssi = ap_info.rssi;
        }
        
        xEventGroupSetBits(g_wifi_manager.wifi_event_group, WIFI_CONNECTED_BIT);
        xEventGroupClearBits(g_wifi_manager.wifi_event_group, WIFI_DISCONNECTED_BIT | WIFI_FAIL_BIT);
        
        if (g_wifi_manager.event_callback) {
            g_wifi_manager.event_callback(ESP_WIFI_STATUS_CONNECTED);
        }
    }
}

esp_err_t esp_wifi_manager_init(void)
{
    if (g_wifi_manager.initialized) {
        TY_LOGW("WiFi manager already initialized");
        return ESP_OK;
    }
    
    // 初始化网络接口
    ESP_ERROR_CHECK(esp_netif_init());
    
    // 创建默认事件循环
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    
    // 创建默认WiFi STA
    g_wifi_manager.sta_netif = esp_netif_create_default_wifi_sta();
    
    // 初始化WiFi
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));
    
    // 创建事件组
    g_wifi_manager.wifi_event_group = xEventGroupCreate();
    
    // 注册事件处理器
    ESP_ERROR_CHECK(esp_event_handler_instance_register(WIFI_EVENT,
                                                        ESP_EVENT_ANY_ID,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        NULL));
    ESP_ERROR_CHECK(esp_event_handler_instance_register(IP_EVENT,
                                                        IP_EVENT_STA_GOT_IP,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        NULL));
    
    // 初始化状态
    g_wifi_manager.current_status = ESP_WIFI_STATUS_IDLE;
    g_wifi_manager.rssi = -100;
    memset(g_wifi_manager.current_ssid, 0, sizeof(g_wifi_manager.current_ssid));
    memset(g_wifi_manager.current_password, 0, sizeof(g_wifi_manager.current_password));
    memset(&g_wifi_manager.ip_addr, 0, sizeof(esp_ip4_addr_t));
    g_wifi_manager.event_callback = NULL;
    g_wifi_manager.initialized = true;
    
    TY_LOGI("WiFi manager initialized successfully");
    return ESP_OK;
}

esp_err_t esp_wifi_manager_deinit(void)
{
    if (!g_wifi_manager.initialized) {
        return ESP_OK;
    }
    
    esp_wifi_stop();
    esp_wifi_deinit();
    
    if (g_wifi_manager.wifi_event_group) {
        vEventGroupDelete(g_wifi_manager.wifi_event_group);
        g_wifi_manager.wifi_event_group = NULL;
    }
    
    g_wifi_manager.initialized = false;
    TY_LOGI("WiFi manager deinitialized");
    return ESP_OK;
}

esp_err_t esp_wifi_manager_set_mode(esp_wifi_mode_t mode)
{
    if (!g_wifi_manager.initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    wifi_mode_t esp_mode;
    switch (mode) {
        case ESP_WIFI_MODE_OFF:
            return esp_wifi_stop();
        case ESP_WIFI_MODE_STA:
            esp_mode = WIFI_MODE_STA;
            break;
        case ESP_WIFI_MODE_AP:
            esp_mode = WIFI_MODE_AP;
            break;
        case ESP_WIFI_MODE_APSTA:
            esp_mode = WIFI_MODE_APSTA;
            break;
        default:
            return ESP_ERR_INVALID_ARG;
    }
    
    esp_err_t ret = esp_wifi_set_mode(esp_mode);
    if (ret == ESP_OK && mode != ESP_WIFI_MODE_OFF) {
        ret = esp_wifi_start();
    }
    
    return ret;
}

esp_err_t esp_wifi_manager_begin(const char* ssid, const char* password)
{
    if (!g_wifi_manager.initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    if (!ssid) {
        return ESP_ERR_INVALID_ARG;
    }
    
    // 保存凭据
    strncpy(g_wifi_manager.current_ssid, ssid, sizeof(g_wifi_manager.current_ssid) - 1);
    if (password) {
        strncpy(g_wifi_manager.current_password, password, sizeof(g_wifi_manager.current_password) - 1);
    } else {
        memset(g_wifi_manager.current_password, 0, sizeof(g_wifi_manager.current_password));
    }
    
    // 配置WiFi
    wifi_config_t wifi_config = {};
    strncpy((char*)wifi_config.sta.ssid, ssid, sizeof(wifi_config.sta.ssid) - 1);
    if (password) {
        strncpy((char*)wifi_config.sta.password, password, sizeof(wifi_config.sta.password) - 1);
    }
    wifi_config.sta.threshold.authmode = WIFI_AUTH_WPA2_PSK;
    wifi_config.sta.pmf_cfg.capable = true;
    wifi_config.sta.pmf_cfg.required = false;
    
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
    ESP_ERROR_CHECK(esp_wifi_start());
    
    g_wifi_manager.current_status = ESP_WIFI_STATUS_CONNECTING;
    
    TY_LOGI("Connecting to WiFi SSID: %s", ssid);
    return esp_wifi_connect();
}

esp_err_t esp_wifi_manager_disconnect(void)
{
    if (!g_wifi_manager.initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    return esp_wifi_disconnect();
}

esp_wifi_status_t esp_wifi_manager_status(void)
{
    return g_wifi_manager.current_status;
}

const char* esp_wifi_manager_get_ssid(void)
{
    return g_wifi_manager.current_ssid;
}

esp_ip4_addr_t esp_wifi_manager_get_local_ip(void)
{
    return g_wifi_manager.ip_addr;
}

std::string esp_wifi_manager_get_local_ip_string(void)
{
    char ip_str[16];
    snprintf(ip_str, sizeof(ip_str), IPSTR, IP2STR(&g_wifi_manager.ip_addr));
    return std::string(ip_str);
}

esp_err_t esp_wifi_manager_set_tx_power(esp_wifi_power_level_t power)
{
    if (!g_wifi_manager.initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    return esp_wifi_set_max_tx_power((int8_t)power);
}

esp_err_t esp_wifi_manager_set_sleep_mode(bool enable)
{
    if (!g_wifi_manager.initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    wifi_ps_type_t ps_type = enable ? WIFI_PS_MIN_MODEM : WIFI_PS_NONE;
    return esp_wifi_set_ps(ps_type);
}

int8_t esp_wifi_manager_get_rssi(void)
{
    if (g_wifi_manager.current_status != ESP_WIFI_STATUS_CONNECTED) {
        return -100;
    }
    
    wifi_ap_record_t ap_info;
    if (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK) {
        g_wifi_manager.rssi = ap_info.rssi;
        return ap_info.rssi;
    }
    
    return g_wifi_manager.rssi;
}

bool esp_wifi_manager_is_connected(void)
{
    return (g_wifi_manager.current_status == ESP_WIFI_STATUS_CONNECTED);
}

void esp_wifi_manager_set_event_callback(wifi_event_callback_t callback)
{
    g_wifi_manager.event_callback = callback;
}
