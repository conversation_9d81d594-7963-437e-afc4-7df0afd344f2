#ifndef WIFI_COMPAT_H
#define WIFI_COMPAT_H

#include "esp_wifi_manager.h"
#include <string>

#ifdef __cplusplus
extern "C" {
#endif

// Arduino WiFi兼容性类
class WiFiCompat {
public:
    // 初始化WiFi
    static bool init();
    
    // 设置WiFi模式
    static bool mode(esp_wifi_mode_t mode);
    
    // 连接到WiFi网络
    static void begin(const char* ssid, const char* password = nullptr);
    
    // 断开WiFi连接
    static bool disconnect(bool wifioff = false, bool eraseap = false);
    
    // 获取WiFi连接状态
    static esp_wifi_status_t status();
    
    // 获取当前连接的SSID
    static std::string SSID();
    
    // 获取本地IP地址
    static std::string localIP();
    
    // 设置发射功率
    static bool setTxPower(esp_wifi_power_level_t power);
    
    // 设置睡眠模式
    static bool setSleep(bool enable);
    
    // 获取RSSI信号强度
    static int32_t RSSI();
    
    // 检查是否已连接
    static bool isConnected();
    
    // 等待连接完成（带超时）
    static bool waitForConnection(uint32_t timeout_ms = 10000);
    
private:
    static bool initialized_;
};

// 全局WiFi对象（兼容Arduino WiFi）
extern WiFiCompat WiFi;

// 兼容性函数
inline bool isWifiConnected() {
    return WiFi.isConnected();
}

// millis()函数兼容性
#ifndef millis
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
inline unsigned long millis() {
    return xTaskGetTickCount() * portTICK_PERIOD_MS;
}
#endif

#ifdef __cplusplus
}
#endif

#endif // WIFI_COMPAT_H
