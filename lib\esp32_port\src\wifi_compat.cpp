#include "wifi_compat.h"
#include "esp_wifi_manager.h"
#include "tuya_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// 静态成员初始化
bool WiFiCompat::initialized_ = false;

bool WiFiCompat::init() {
    if (initialized_) {
        return true;
    }
    
    esp_err_t ret = esp_wifi_manager_init();
    if (ret == ESP_OK) {
        initialized_ = true;
        TY_LOGI("WiFi compatibility layer initialized");
        return true;
    } else {
        TY_LOGE("Failed to initialize WiFi compatibility layer: %s", esp_err_to_name(ret));
        return false;
    }
}

bool WiFiCompat::mode(esp_wifi_mode_t mode) {
    if (!initialized_ && !init()) {
        return false;
    }
    
    esp_err_t ret = esp_wifi_manager_set_mode(mode);
    if (ret != ESP_OK) {
        TY_LOGE("Failed to set WiFi mode: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

void WiFiCompat::begin(const char* ssid, const char* password) {
    if (!initialized_ && !init()) {
        TY_LOGE("WiFi not initialized");
        return;
    }
    
    esp_err_t ret = esp_wifi_manager_begin(ssid, password);
    if (ret != ESP_OK) {
        TY_LOGE("Failed to begin WiFi connection: %s", esp_err_to_name(ret));
    }
}

bool WiFiCompat::disconnect(bool wifioff, bool eraseap) {
    if (!initialized_) {
        return true;
    }
    
    esp_err_t ret = esp_wifi_manager_disconnect();
    if (ret != ESP_OK) {
        TY_LOGE("Failed to disconnect WiFi: %s", esp_err_to_name(ret));
        return false;
    }
    
    if (wifioff) {
        ret = esp_wifi_manager_set_mode(ESP_WIFI_MODE_OFF);
        if (ret != ESP_OK) {
            TY_LOGE("Failed to turn off WiFi: %s", esp_err_to_name(ret));
            return false;
        }
    }
    
    return true;
}

esp_wifi_status_t WiFiCompat::status() {
    if (!initialized_) {
        return ESP_WIFI_STATUS_IDLE;
    }
    
    return esp_wifi_manager_status();
}

std::string WiFiCompat::SSID() {
    if (!initialized_) {
        return "";
    }
    
    const char* ssid = esp_wifi_manager_get_ssid();
    return std::string(ssid ? ssid : "");
}

std::string WiFiCompat::localIP() {
    if (!initialized_) {
        return "0.0.0.0";
    }
    
    return esp_wifi_manager_get_local_ip_string();
}

bool WiFiCompat::setTxPower(esp_wifi_power_level_t power) {
    if (!initialized_) {
        return false;
    }
    
    esp_err_t ret = esp_wifi_manager_set_tx_power(power);
    if (ret != ESP_OK) {
        TY_LOGE("Failed to set TX power: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

bool WiFiCompat::setSleep(bool enable) {
    if (!initialized_) {
        return false;
    }
    
    esp_err_t ret = esp_wifi_manager_set_sleep_mode(enable);
    if (ret != ESP_OK) {
        TY_LOGE("Failed to set sleep mode: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

int32_t WiFiCompat::RSSI() {
    if (!initialized_) {
        return -100;
    }
    
    return esp_wifi_manager_get_rssi();
}

bool WiFiCompat::isConnected() {
    if (!initialized_) {
        return false;
    }
    
    return esp_wifi_manager_is_connected();
}

bool WiFiCompat::waitForConnection(uint32_t timeout_ms) {
    if (!initialized_) {
        return false;
    }
    
    uint32_t start_time = millis();
    while ((millis() - start_time) < timeout_ms) {
        if (isConnected()) {
            return true;
        }
        vTaskDelay(pdMS_TO_TICKS(100));
    }
    
    return false;
}

// 全局WiFi对象
WiFiCompat WiFi;
