#ifndef ESP_WIFI_MANAGER_H
#define ESP_WIFI_MANAGER_H

#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_netif.h"
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include <string>

#ifdef __cplusplus
extern "C" {
#endif

// WiFi状态定义
typedef enum {
    ESP_WIFI_STATUS_IDLE = 0,
    ESP_WIFI_STATUS_CONNECTING,
    ESP_WIFI_STATUS_CONNECTED,
    ESP_WIFI_STATUS_DISCONNECTED,
    ESP_WIFI_STATUS_CONNECTION_FAILED
} esp_wifi_status_t;

// WiFi模式定义
typedef enum {
    ESP_WIFI_MODE_OFF = 0,
    ESP_WIFI_MODE_STA,
    ESP_WIFI_MODE_AP,
    ESP_WIFI_MODE_APSTA
} esp_wifi_mode_t;

// WiFi功率等级定义
typedef enum {
    ESP_WIFI_POWER_2dBm = 8,     // 2dBm
    ESP_WIFI_POWER_5dBm = 20,    // 5dBm  
    ESP_WIFI_POWER_8_5dBm = 34,  // 8.5dBm
    ESP_WIFI_POWER_11dBm = 44,   // 11dBm
    ESP_WIFI_POWER_13dBm = 52,   // 13dBm
    ESP_WIFI_POWER_15dBm = 60,   // 15dBm
    ESP_WIFI_POWER_17dBm = 68,   // 17dBm
    ESP_WIFI_POWER_19_5dBm = 78, // 19.5dBm (最大功率)
} esp_wifi_power_level_t;

// WiFi事件回调函数类型
typedef void (*wifi_event_callback_t)(esp_wifi_status_t status);

// WiFi管理器结构体
typedef struct {
    esp_netif_t *sta_netif;
    esp_netif_t *ap_netif;
    EventGroupHandle_t wifi_event_group;
    wifi_event_callback_t event_callback;
    esp_wifi_status_t current_status;
    char current_ssid[33];
    char current_password[65];
    esp_ip4_addr_t ip_addr;
    int8_t rssi;
    bool initialized;
} esp_wifi_manager_t;

// WiFi事件位定义
#define WIFI_CONNECTED_BIT    BIT0
#define WIFI_FAIL_BIT         BIT1
#define WIFI_DISCONNECTED_BIT BIT2

// 函数声明
esp_err_t esp_wifi_manager_init(void);
esp_err_t esp_wifi_manager_deinit(void);
esp_err_t esp_wifi_manager_set_mode(esp_wifi_mode_t mode);
esp_err_t esp_wifi_manager_begin(const char* ssid, const char* password);
esp_err_t esp_wifi_manager_disconnect(void);
esp_wifi_status_t esp_wifi_manager_status(void);
const char* esp_wifi_manager_get_ssid(void);
esp_ip4_addr_t esp_wifi_manager_get_local_ip(void);
std::string esp_wifi_manager_get_local_ip_string(void);
esp_err_t esp_wifi_manager_set_tx_power(esp_wifi_power_level_t power);
esp_err_t esp_wifi_manager_set_sleep_mode(bool enable);
int8_t esp_wifi_manager_get_rssi(void);
bool esp_wifi_manager_is_connected(void);
void esp_wifi_manager_set_event_callback(wifi_event_callback_t callback);

// 兼容性宏定义（用于替换Arduino WiFi函数）
#define WL_CONNECTED ESP_WIFI_STATUS_CONNECTED
#define WL_DISCONNECTED ESP_WIFI_STATUS_DISCONNECTED
#define WL_CONNECTION_LOST ESP_WIFI_STATUS_DISCONNECTED
#define WL_CONNECT_FAILED ESP_WIFI_STATUS_CONNECTION_FAILED
#define WL_IDLE_STATUS ESP_WIFI_STATUS_IDLE

#define WIFI_OFF ESP_WIFI_MODE_OFF
#define WIFI_STA ESP_WIFI_MODE_STA
#define WIFI_AP ESP_WIFI_MODE_AP
#define WIFI_AP_STA ESP_WIFI_MODE_APSTA

#define WIFI_POWER_2dBm ESP_WIFI_POWER_2dBm
#define WIFI_POWER_5dBm ESP_WIFI_POWER_5dBm
#define WIFI_POWER_8_5dBm ESP_WIFI_POWER_8_5dBm
#define WIFI_POWER_11dBm ESP_WIFI_POWER_11dBm
#define WIFI_POWER_13dBm ESP_WIFI_POWER_13dBm
#define WIFI_POWER_15dBm ESP_WIFI_POWER_15dBm
#define WIFI_POWER_17dBm ESP_WIFI_POWER_17dBm
#define WIFI_POWER_19_5dBm ESP_WIFI_POWER_19_5dBm

#ifdef __cplusplus
}
#endif

#endif // ESP_WIFI_MANAGER_H
